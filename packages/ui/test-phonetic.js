// Simple test for phonetic transcription
const pinyin = require('pinyin')
const wanakana = require('wanakana')

console.log('Pinyin library:', typeof pinyin, Object.keys(pinyin))
console.log('Wanakana library:', typeof wanakana, Object.keys(wanakana))

// Test Chinese to Pinyin
console.log('Testing Chinese to Pinyin:')
const testChinese = (text) => {
  const pinyinResult = pinyin.default
    ? pinyin.default(text, {
        style: pinyin.STYLE_TONE,
        heteronym: false,
        segment: true
      })
    : pinyin(text, {
        style: pinyin.STYLE_TONE,
        heteronym: false,
        segment: true
      })
  return pinyinResult.map((item) => (Array.isArray(item) ? item[0] : item)).join(' ')
}

console.log('朋友你好 ->', testChinese('朋友你好'))
console.log('我爱你 ->', testChinese('我爱你'))
console.log('谢谢 ->', testChinese('谢谢'))

// Test Japanese to Romaji
console.log('\nTesting Japanese to Romaji:')
console.log('こんにちは ->', wanakana.toRomaji('こんにちは'))
console.log('ありがとう ->', wanakana.toRomaji('ありがとう'))
console.log('さようなら ->', wanakana.toRomaji('さようなら'))

console.log('\nPhonetic transcription libraries are working correctly!')
