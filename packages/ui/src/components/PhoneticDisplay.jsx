import React, { useState } from 'react'
import PropTypes from 'prop-types'
import { Box, Typography, IconButton, Tooltip, Collapse } from '@mui/material'
import { IconLanguage, IconLanguageOff } from '@tabler/icons-react'

const PhoneticDisplay = ({ originalText, phoneticText, language, showPhonetic = true, onTogglePhonetic }) => {
  const [isExpanded, setIsExpanded] = useState(showPhonetic)

  const handleToggle = () => {
    const newState = !isExpanded
    setIsExpanded(newState)
    if (onTogglePhonetic) {
      onTogglePhonetic(newState)
    }
  }

  const getLanguageLabel = (lang) => {
    switch (lang) {
      case 'Trung':
        return 'Pinyin'
      case 'Nhật':
        return 'Romaji'
      default:
        return 'Phonetic'
    }
  }

  const getPhoneticStyle = (lang) => {
    switch (lang) {
      case 'Trung':
        return {
          fontFamily: '"Segoe UI", "Microsoft YaHei", "PingFang SC", sans-serif',
          fontSize: '16px',
          color: '#5f6368',
          fontWeight: 400
        }
      case 'Nhật':
        return {
          fontFamily: '"Segoe UI", "Hiragino Sans", "Yu Gothic", sans-serif',
          fontSize: '16px',
          color: '#5f6368',
          fontWeight: 400,
          fontStyle: 'italic'
        }
      default:
        return {
          fontSize: '16px',
          color: '#5f6368',
          fontWeight: 400
        }
    }
  }

  if (!phoneticText || phoneticText.trim().length === 0) {
    return (
      <Typography
        variant='body1'
        sx={{
          fontSize: '18px',
          lineHeight: '32px',
          color: '#3c4043',
          mb: 2
        }}
      >
        {originalText || 'Bản dịch sẽ xuất hiện ở đây'}
      </Typography>
    )
  }

  return (
    <Box sx={{ mb: 2 }}>
      {/* Main translated text */}
      <Typography
        variant='body1'
        sx={{
          fontSize: '18px',
          lineHeight: '32px',
          color: '#3c4043',
          mb: 1
        }}
      >
        {originalText}
      </Typography>

      {/* Phonetic transcription section */}
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
        <Tooltip title={isExpanded ? `Ẩn ${getLanguageLabel(language)}` : `Hiện ${getLanguageLabel(language)}`}>
          <IconButton
            size='small'
            onClick={handleToggle}
            sx={{
              color: '#5f6368',
              '&:hover': {
                backgroundColor: 'rgba(95, 99, 104, 0.1)'
              },
              mt: 0.5
            }}
          >
            {isExpanded ? <IconLanguageOff size={18} /> : <IconLanguage size={18} />}
          </IconButton>
        </Tooltip>

        <Box sx={{ flex: 1 }}>
          <Collapse in={isExpanded}>
            <Box
              sx={{
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                padding: '12px 16px',
                border: '1px solid #e8eaed'
              }}
            >
              {/* <Typography
                variant='caption'
                sx={{
                  color: '#5f6368',
                  fontSize: '12px',
                  fontWeight: 500,
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px',
                  display: 'block',
                  mb: 0.5
                }}
              >
                {getLanguageLabel(language)}
              </Typography> */}
              <Typography
                sx={{
                  ...getPhoneticStyle(language),
                  lineHeight: '24px',
                  wordBreak: 'break-word'
                }}
              >
                {phoneticText}
              </Typography>
            </Box>
          </Collapse>
        </Box>
      </Box>
    </Box>
  )
}

PhoneticDisplay.propTypes = {
  originalText: PropTypes.string,
  phoneticText: PropTypes.string,
  language: PropTypes.string,
  showPhonetic: PropTypes.bool,
  onTogglePhonetic: PropTypes.func
}

export default PhoneticDisplay
