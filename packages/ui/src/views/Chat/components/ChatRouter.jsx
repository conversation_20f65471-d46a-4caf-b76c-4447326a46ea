import { memo } from 'react'
import { useParams, useLocation } from 'react-router-dom'
import { Box, Typography, Stack } from '@mui/material'
import ChatContent from './ChatContent'
import ChatbotEmbed from './ChatbotEmbed'

const ChatRouter = () => {
  const { chatFlowId, chatSessionId } = useParams()
  const location = useLocation()

  // Check if we're on the translation route
  if (location.pathname === '/chat/translation') {
    return (
      <Box sx={{ height: 'calc(100vh - 57px)', overflow: 'hidden' }}>
        <ChatContent />
      </Box>
    )
  }

  // Check if we have a chatflow selected
  if (chatFlowId) {
    return (
      <Box
        sx={{
          zIndex: '1200'
        }}
      >
        <ChatbotEmbed chatflowId={chatFlowId} />
      </Box>
    )
  }

  // Default welcome screen
  return (
    <Box
      sx={{
        height: 'calc(100dvh - 57px)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: 2,
        p: 4
      }}
    >
      <Typography variant='h4' color='text.secondary' textAlign='center'>
        Chào mừng đến với Chat AI
      </Typography>
      <Typography variant='body1' color='text.secondary' textAlign='center' maxWidth='600px'>
        Chọn một dịch vụ từ sidebar để bắt đầu:
      </Typography>
      <Stack spacing={1} sx={{ mt: 2 }}>
        <Typography variant='body2' color='text.secondary'>
          • <strong>Dịch thuật:</strong> Dịch văn bản và tài liệu giữa các ngôn ngữ
        </Typography>
        <Typography variant='body2' color='text.secondary'>
          • <strong>AI Agents:</strong> Trò chuyện với các AI agent chuyên biệt
        </Typography>
      </Stack>
    </Box>
  )
}

export default memo(ChatRouter)
