import ModalPreviewImage from '@/ui-component/modal/ModalPreviewImage'
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  IconButton,
  LinearProgress,
  Paper,
  Stack,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography
} from '@mui/material'
import {
  IconArrowsExchange,
  IconDownload,
  IconLayoutDistributeHorizontal,
  IconLayoutDistributeVertical,
  IconUpload
} from '@tabler/icons-react'
import { memo, useState, useRef, useEffect, useCallback } from 'react'
import PhoneticDisplay from '../../../components/PhoneticDisplay'
import { getPhoneticTranscription, supportsPhoneticTranscription } from '../../../utils/phoneticTranscription'
import mammoth from 'mammoth'
import debounce from 'lodash/debounce'
import JSZip from 'jszip'
import { XMLParser, XMLBuilder } from 'fast-xml-parser'
import { saveAs } from 'file-saver'
import <PERSON>z<PERSON><PERSON> from 'pizzip'

// Utility function to decode HTML entities for display
const decodeHtmlEntities = (text) => {
  if (!text || typeof text !== 'string') return text

  return text
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&apos;/g, "'")
}

const ChatContent = () => {
  const [previewImage, setPreviewImage] = useState({
    open: false,
    image: ''
  })

  const [activeTab, setActiveTab] = useState('Văn bản')
  const [leftLang, setLeftLang] = useState('Việt')
  const [rightLang, setRightLang] = useState('Anh')
  const [textContent, setTextContent] = useState('')
  const [translatedText, setTranslatedText] = useState('')
  const [layout, setLayout] = useState('horizontal')
  const [paperHeight, setPaperHeight] = useState(200)
  const [isDragOver, setIsDragOver] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isTranslating, setIsTranslating] = useState(false)
  const [originalFile, setOriginalFile] = useState(null)
  const [_originalFileBuffer, setOriginalFileBuffer] = useState(null)
  const [hasTranslatedFile, setHasTranslatedFile] = useState(false)
  const [translatedFileBlob, setTranslatedFileBlob] = useState(null)
  const [translatedFileName, setTranslatedFileName] = useState('')
  const [translationStatus, setTranslationStatus] = useState('')
  const [translationProgress, setTranslationProgress] = useState(0)
  const [totalChunks, setTotalChunks] = useState(0)
  const [processedChunks, setProcessedChunks] = useState(0)
  const [isChunkedTranslation, setIsChunkedTranslation] = useState(false)
  const [_chunkDetails, setChunkDetails] = useState([])
  const [phoneticText, setPhoneticText] = useState('')
  const [showPhonetic, setShowPhonetic] = useState(true)
  const [isGeneratingPhonetic, setIsGeneratingPhonetic] = useState(false)
  const textFieldRef = useRef(null)
  const textAreaRef = useRef(null)
  const fileInputRef = useRef(null)
  const tabs = ['Văn bản', 'Tài liệu']
  const textLanguages = ['Việt', 'Anh', 'Nhật', 'Trung']
  const fileLanguages = ['Phát hiện ngôn ngữ', 'Việt', 'Anh', 'Nhật', 'Trung']

  // Translation API endpoint - using consistent endpoint
  const TRANSLATION_API_Text_ENDPOINT = 'https://kyoceravn.cagent.cmcts.ai/api/v1/prediction/fe2106dc-246e-47f2-acda-4a4d78568c41'

  const TRANSLATION_API_FILE_ENDPOINT = 'https://kyoceravn.cagent.cmcts.ai/api/v1/prediction/a3e05794-5cb0-498d-bbaa-276ce3f99fad'

  const translateText = useCallback(
    async (text, fromLang, toLang) => {
      if (!text.trim()) {
        setIsTranslating(false)
        return setTranslatedText('')
      }

      setIsTranslating(true)
      try {
        if (!textAreaRef.current.value) {
          setTranslatedText('')
          setIsTranslating(false)
          return
        }
        const response = await fetch(TRANSLATION_API_Text_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            question: text.trim(),
            overrideConfig: {
              promptValues: {
                input_language: fromLang,
                output_language: toLang
              }
            }
          })
        })

        if (!textAreaRef.current.value) {
          setTranslatedText('')
          setIsTranslating(false)
          return
        }

        if (text.trim()) {
          const result = await response.json()
          const translatedContent = result?.json?.translated_text || 'Không thể dịch văn bản'
          // Decode HTML entities before displaying to user
          const decodedTranslatedContent = decodeHtmlEntities(translatedContent)
          setTranslatedText(decodedTranslatedContent)

          // Generate phonetic transcription for Chinese and Japanese
          if (supportsPhoneticTranscription(toLang) && translatedContent && translatedContent !== 'Không thể dịch văn bản') {
            setIsGeneratingPhonetic(true)
            try {
              const phonetic = await getPhoneticTranscription(translatedContent, toLang)
              setPhoneticText(phonetic)
            } catch (error) {
              console.error('Error generating phonetic transcription:', error)
              setPhoneticText('')
            } finally {
              setIsGeneratingPhonetic(false)
            }
          } else {
            setPhoneticText('')
          }

          // Set hasTranslatedFile to true if we have an original file and successful translation
          if (originalFile && translatedContent && translatedContent !== 'Không thể dịch văn bản') {
            setHasTranslatedFile(true)
          }
        } else {
          return
        }
      } catch (error) {
        console.error('Translation error:', error)
        setTranslatedText('Đã xảy ra lỗi khi dịch văn bản')
        setPhoneticText('')
        setHasTranslatedFile(false)
      } finally {
        setIsTranslating(false)
      }
    },
    [originalFile, TRANSLATION_API_Text_ENDPOINT]
  )

  const debouncedTranslate = useCallback(
    debounce((text, fromLang, toLang) => {
      translateText(text, fromLang, toLang)
    }, 500),
    []
  )
  // Utility function to process chunks in parallel with limited concurrency
  const processChunksInParallel = useCallback(async (chunks, maxConcurrent, processChunk, updateProgress) => {
    const results = new Array(chunks.length)
    let activeCount = 0
    let nextIndex = 0

    // Returns a promise that resolves when all chunks are processed
    return new Promise((resolve) => {
      // Function to start processing the next chunk
      const startNextChunk = () => {
        if (nextIndex >= chunks.length) return // No more chunks to process

        const currentIndex = nextIndex++
        activeCount++

        // Process this chunk
        processChunk(chunks[currentIndex], currentIndex)
          .then((result) => {
            results[currentIndex] = result // Store result in correct order

            // Count completed chunks
            const completedCount = results.filter((r) => r !== undefined).length
            updateProgress(completedCount, chunks.length) // Update progress

            // This chunk is done, reduce active count
            activeCount--

            // Start next chunk if any
            startNextChunk()

            // If all chunks are done, resolve the main promise
            if (activeCount === 0 && nextIndex >= chunks.length) {
              resolve(results)
            }
          })
          .catch((error) => {
            console.error(`Error processing chunk ${currentIndex}:`, error)

            // Store error result but mark as failed
            results[currentIndex] = {
              error: error.message,
              success: false,
              index: currentIndex,
              originalChunk: chunks[currentIndex]
            }

            // Count completed chunks (including errors)
            const completedCount = results.filter((r) => r !== undefined).length
            updateProgress(completedCount, chunks.length)

            // This chunk is done (with error), reduce active count
            activeCount--

            // Start next chunk if any
            startNextChunk()

            // If all chunks are done, check for errors before resolving
            if (activeCount === 0 && nextIndex >= chunks.length) {
              const failedChunks = results.filter((r) => r.error)
              if (failedChunks.length > 0) {
                reject(new Error(`Failed to translate ${failedChunks.length} chunks. First error: ${failedChunks[0].error}`))
              } else {
                resolve(results)
              }
            }
          })
      }

      // Start initial batch of chunks (up to maxConcurrent)
      for (let i = 0; i < Math.min(maxConcurrent, chunks.length); i++) {
        startNextChunk()
      }

      // If no chunks to process, resolve immediately
      if (chunks.length === 0) {
        resolve([])
      }
    })
  }, [])

  // Split text into chunks while preserving original formatting
  const createTextChunks = useCallback((text, maxChunkSize = 2000) => {
    if (text.length <= maxChunkSize) {
      return [
        {
          text: text.trim(),
          originalText: text,
          index: 0,
          isLarge: false,
          startPos: 0,
          endPos: text.length
        }
      ]
    }

    const chunks = []
    let chunkIndex = 0
    let currentPos = 0

    while (currentPos < text.length) {
      let chunkEnd = Math.min(currentPos + maxChunkSize, text.length)

      // If we're not at the end, try to find a good break point
      if (chunkEnd < text.length) {
        // Look for paragraph breaks first (double newlines)
        let lastParagraphBreak = text.lastIndexOf('\n\n', chunkEnd)
        if (lastParagraphBreak > currentPos) {
          chunkEnd = lastParagraphBreak + 2 // Include the double newline
        } else {
          // Look for single line breaks
          let lastLineBreak = text.lastIndexOf('\n', chunkEnd)
          if (lastLineBreak > currentPos) {
            chunkEnd = lastLineBreak + 1 // Include the newline
          } else {
            // Look for sentence endings
            let lastSentenceEnd = Math.max(
              text.lastIndexOf('. ', chunkEnd),
              text.lastIndexOf('! ', chunkEnd),
              text.lastIndexOf('? ', chunkEnd)
            )
            if (lastSentenceEnd > currentPos) {
              chunkEnd = lastSentenceEnd + 2 // Include the punctuation and space
            } else {
              // Look for word boundaries
              let lastSpace = text.lastIndexOf(' ', chunkEnd)
              if (lastSpace > currentPos) {
                chunkEnd = lastSpace + 1 // Include the space
              }
              // If no good break point found, just cut at maxChunkSize
            }
          }
        }
      }

      const chunkText = text.substring(currentPos, chunkEnd)
      const trimmedText = chunkText.trim()

      if (trimmedText.length > 0) {
        chunks.push({
          text: trimmedText,
          originalText: chunkText, // Keep original with all whitespace/formatting
          index: chunkIndex,
          isLarge: chunks.length > 0 || chunkEnd < text.length,
          startPos: currentPos,
          endPos: chunkEnd
        })
        chunkIndex++
      }

      currentPos = chunkEnd
    }

    return chunks
  }, [])

  // New parallel translation function with context preservation for TXT files
  const translateChunksInParallel = useCallback(
    async (textChunks, sourceLanguage, targetLanguage, progressCallback) => {
      // Add context padding to chunks for better translation quality
      const chunksWithContext = textChunks.map((chunk, index) => {
        let contextPrefix = ''
        let contextSuffix = ''

        // Add previous chunk context (last 2 sentences)
        if (index > 0) {
          const prevChunk = textChunks[index - 1]
          const prevSentences = prevChunk.text.split(/[.!?]+/).filter((s) => s.trim())
          contextPrefix = prevSentences.slice(-2).join('. ').trim()
        }

        // Add next chunk context (first 2 sentences)
        if (index < textChunks.length - 1) {
          const nextChunk = textChunks[index + 1]
          const nextSentences = nextChunk.text.split(/[.!?]+/).filter((s) => s.trim())
          contextSuffix = nextSentences.slice(0, 2).join('. ').trim()
        }

        return {
          ...chunk,
          contextPrefix,
          contextSuffix,
          textForTranslation: chunk.text,
          // Keep the original formatting info
          originalText: chunk.originalText || chunk.text
        }
      })

      // Define the chunk processing function
      const processChunk = async (chunk, chunkIndex) => {
        try {
          // Prepare promptValues - exclude input_language if using language detection
          const promptValues = {
            output_language: targetLanguage
          }

          // Only add input_language if not using language detection
          if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
            promptValues.input_language = sourceLanguage
          }

          const response = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              question: chunk.textForTranslation || chunk.text,
              overrideConfig: {
                promptValues: promptValues
              }
            })
          })

          const apiResult = await response.json()
          let translatedContent = apiResult?.text

          // Only return success if we actually got a translation
          if (translatedContent && translatedContent.trim()) {
            return {
              ...chunk,
              // Store original translated text for internal processing, but decode for user display
              translatedText: translatedContent,
              success: true,
              index: chunkIndex
            }
          } else {
            // If translation failed, throw error to trigger retry
            throw new Error('API returned empty or invalid translation')
          }
        } catch (error) {
          console.error(`Translation error for chunk ${chunkIndex}:`, error)

          // Retry once with simpler prompt
          try {
            // Prepare promptValues for retry - exclude input_language if using language detection
            const retryPromptValues = {
              output_language: targetLanguage
            }

            // Only add input_language if not using language detection
            if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
              retryPromptValues.input_language = sourceLanguage
            }

            const retryResponse = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                question: chunk.textForTranslation || chunk.text,
                overrideConfig: {
                  promptValues: retryPromptValues
                }
              })
            })

            const retryResult = await retryResponse.json()
            let retryTranslation = retryResult?.json?.translated_text

            if (retryTranslation && retryTranslation.trim()) {
              return {
                ...chunk,
                // Store original translated text for internal processing
                translatedText: retryTranslation,
                success: true,
                index: chunkIndex,
                retried: true
              }
            }
          } catch (retryError) {
            console.error(`Retry failed for chunk ${chunkIndex}:`, retryError)
          }

          // If both attempts failed, throw error to stop the process
          throw new Error(`Failed to translate chunk ${chunkIndex}: ${error.message}`)
        }
      }

      // Update progress function for TXT files
      const updateProgress = (completedCount, totalChunks) => {
        progressCallback(completedCount, totalChunks)
      }

      // Process chunks in parallel with max 5 concurrent requests
      try {
        const results = await processChunksInParallel(chunksWithContext, 5, processChunk, updateProgress)

        // Filter out any error results and sort by original index to maintain order
        const successfulResults = results.filter((r) => r.success !== false)

        if (successfulResults.length === 0) {
          throw new Error('All chunks failed to translate')
        }

        if (successfulResults.length < results.length) {
          console.warn(`${results.length - successfulResults.length} chunks failed to translate`)
        }

        return successfulResults.sort((a, b) => a.index - b.index)
      } catch (error) {
        console.error('Translation failed:', error)
        throw error
      }
    },
    [TRANSLATION_API_FILE_ENDPOINT, processChunksInParallel]
  )

  const combineTranslatedChunks = useCallback((translatedChunks) => {
    if (!translatedChunks?.length) {
      return ''
    }

    if (translatedChunks.length === 1) {
      // Decode HTML entities before returning to user
      return decodeHtmlEntities(translatedChunks[0].translatedText)
    }

    const sortedChunks = translatedChunks.sort((a, b) => a.index - b.index)

    // Method 1: Use original text structure to rebuild with proper formatting
    let result = ''

    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i]
      // Decode HTML entities for each chunk before combining
      const translatedText = decodeHtmlEntities(chunk.translatedText || '')
      const originalText = chunk.originalText || ''

      if (i === 0) {
        // For first chunk, analyze leading whitespace from original
        const leadingWhitespace = originalText.match(/^(\s*)/)[1]
        result = leadingWhitespace + translatedText
      } else {
        // For subsequent chunks, analyze the boundary between previous and current
        const prevChunk = sortedChunks[i - 1]
        const prevOriginal = prevChunk.originalText || ''

        // Extract the whitespace/formatting between chunks
        const prevTrailing = prevOriginal.match(/(\s*)$/)[1]
        const currentLeading = originalText.match(/^(\s*)/)[1]

        // Combine the boundary formatting
        let separator = prevTrailing + currentLeading

        // If no explicit separator found, try to infer from content
        if (!separator) {
          if (prevOriginal.trim().endsWith('.') || prevOriginal.trim().endsWith('!') || prevOriginal.trim().endsWith('?')) {
            separator = ' '
          } else {
            separator = ' '
          }
        }

        result += separator + translatedText
      }
    }

    // Method 2: If method 1 doesn't work well, try reconstructing based on original structure
    if (!result.trim()) {
      // Fallback to simpler approach - decode each chunk before joining
      result = sortedChunks.map((chunk) => decodeHtmlEntities(chunk.translatedText)).join(' ')
    }

    return result
  }, [])

  const handleLayoutChange = (_, newLayout) => {
    if (newLayout !== null) {
      setLayout(newLayout)
    }
  }

  const updatePaperHeight = useCallback(() => {
    const textArea = textAreaRef.current
    if (textArea) {
      textArea.style.height = 'auto'
      const newHeight = Math.max(200, textArea.scrollHeight)
      textArea.style.height = `${newHeight}px`
      setPaperHeight(newHeight + 80)
    }
  }, [])

  const handleTextChange = (e) => {
    const inputText = e.target.value
    setTextContent(inputText)
    updatePaperHeight()

    // Clear file state when user manually types (not from file upload)
    if (originalFile) {
      resetFileState()
    }

    if (!inputText.trim()) {
      setIsTranslating(false)
      setTranslatedText('')
      setPhoneticText('')
      setHasTranslatedFile(false)
    } else if (activeTab === 'Văn bản') {
      // Only call text translation API when on text translation tab
      debouncedTranslate(inputText, leftLang, rightLang)
    }
  }

  const resetFileState = useCallback(() => {
    setOriginalFile(null)
    setOriginalFileBuffer(null)
    setHasTranslatedFile(false)
    setTranslationProgress(0)
    setTotalChunks(0)
    setProcessedChunks(0)
    setIsChunkedTranslation(false)
    setChunkDetails([])
    setTranslationStatus('')
    setTranslatedFileBlob(null)
    setTranslatedFileName('')
    setPhoneticText('')
  }, [])

  // File reading functions
  const readTextFile = useCallback((file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  }, [])

  const readDocxFile = useCallback((file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target.result
          const result = await mammoth.extractRawText({ arrayBuffer })
          resolve(result.value)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }, [])

  const createTranslatedDocxFile = useCallback(
    async (arrayBuffer, originalFileName, sourceLanguage, targetLanguage) => {
      try {
        // Use JSZip to extract DOCX (browser-compatible)
        const docxZip = new JSZip()
        await docxZip.loadAsync(arrayBuffer)

        // Read ALL XML files that may contain text content
        const xmlFiles = {
          'word/document.xml': null,
          'word/header1.xml': null,
          'word/header2.xml': null,
          'word/header3.xml': null,
          'word/footer1.xml': null,
          'word/footer2.xml': null,
          'word/footer3.xml': null,
          'word/footnotes.xml': null,
          'word/endnotes.xml': null
        }

        // Read existing XML files
        for (const filePath of Object.keys(xmlFiles)) {
          const xmlFile = docxZip.file(filePath)
          if (xmlFile) {
            xmlFiles[filePath] = await xmlFile.async('text')
            console.log(`Found and loaded: ${filePath}`)
          }
        }

        // Ensure document.xml exists
        if (!xmlFiles['word/document.xml']) {
          throw new Error('Document.xml not found in DOCX file')
        }

        const documentXml = xmlFiles['word/document.xml']

        // Log original XML structure for debugging
        console.log('Original XML namespaces:', documentXml.substring(0, 200))

        // Parse XML to JSON with proper configuration for DOCX
        const xmlParser = new XMLParser({
          ignoreAttributes: false,
          attributeNamePrefix: '@_',
          textNodeName: '#text',
          preserveOrder: true,
          parseTagValue: false,
          parseAttributeValue: false,
          trimValues: false,
          parseTrueNumberOnly: false,
          processEntities: false,
          htmlEntities: false,
          ignoreNameSpace: false,
          allowBooleanAttributes: true,
          parseNodeValue: false
        })
        // Parse ALL XML files to JSON
        const parsedXmlFiles = {}
        for (const [filePath, xmlContent] of Object.entries(xmlFiles)) {
          if (xmlContent) {
            parsedXmlFiles[filePath] = xmlParser.parse(xmlContent)
          }
        }

        const documentJsonObj = parsedXmlFiles['word/document.xml']

        // Extract paragraphs for translation from ALL XML files
        const paragraphs = []

        // Function to extract paragraphs from any XML document
        const extractParagraphs = (obj) => {
          if (!obj || typeof obj !== 'object') return

          if (Array.isArray(obj)) {
            for (const item of obj) {
              extractParagraphs(item)
            }
            return
          }

          // Check if this is a paragraph element
          if (obj['w:p']) {
            const paragraph = {
              element: obj['w:p'],
              originalText: '',
              textNodes: [], // Direct references to text nodes in the document
              hasNumbering: false,
              hasSpecialFormatting: false,
              isInFieldContext: false,
              fieldDepth: 0
            }

            // Check for paragraph properties that should be preserved
            const paraElement = obj['w:p']
            if (Array.isArray(paraElement)) {
              for (const item of paraElement) {
                if (item['w:pPr']) {
                  // Check for numbering (lists, bullets)
                  if (item['w:pPr']['w:numPr']) {
                    paragraph.hasNumbering = true
                  }
                  // Check for special formatting
                  if (item['w:pPr']['w:pStyle'] || item['w:pPr']['w:spacing'] || item['w:pPr']['w:ind']) {
                    paragraph.hasSpecialFormatting = true
                  }
                }
              }
            } else if (paraElement['w:pPr']) {
              if (paraElement['w:pPr']['w:numPr']) {
                paragraph.hasNumbering = true
              }
              if (paraElement['w:pPr']['w:pStyle'] || paraElement['w:pPr']['w:spacing'] || paraElement['w:pPr']['w:ind']) {
                paragraph.hasSpecialFormatting = true
              }
            }

            // Enhanced text extraction that preserves ALL formatting elements
            const extractTextFromParagraph = (paraElement) => {
              if (Array.isArray(paraElement)) {
                for (const item of paraElement) {
                  extractTextFromParagraph(item)
                }
              } else if (paraElement['w:r']) {
                const runs = Array.isArray(paraElement['w:r']) ? paraElement['w:r'] : [paraElement['w:r']]

                for (const run of runs) {
                  // CRITICAL: Track field context FIRST
                  if (run['w:fldChar']) {
                    const fldCharType = run['w:fldChar']['@_w:fldCharType']
                    if (fldCharType === 'begin') {
                      paragraph.fieldDepth++
                      paragraph.isInFieldContext = true
                    } else if (fldCharType === 'end') {
                      paragraph.fieldDepth--
                      if (paragraph.fieldDepth <= 0) {
                        paragraph.isInFieldContext = false
                        paragraph.fieldDepth = 0
                      }
                    }
                    console.log('Skipping field character:', fldCharType)
                    continue // Skip field characters
                  }

                  // Skip field instructions
                  if (run['w:instrText']) {
                    console.log('Skipping field instruction:', run['w:instrText']['#text'] || run['w:instrText'])
                    continue // Skip field instructions
                  }

                  // CRITICAL: Skip field values when inside field context
                  if (paragraph.isInFieldContext && run['w:t']) {
                    console.log('Skipping field value:', run['w:t']['#text'] || run['w:t'])
                    continue // Skip field values like "8", "23"
                  }

                  // Check for text elements (only if not in field context)
                  if (run['w:t'] && !paragraph.isInFieldContext) {
                    const textElements = Array.isArray(run['w:t']) ? run['w:t'] : [run['w:t']]

                    for (const textEl of textElements) {
                      if (textEl['#text'] !== undefined) {
                        let text = textEl['#text'] || ''

                        // CRITICAL: Decode HTML entities
                        text = text
                          .replace(/&lt;/g, '<')
                          .replace(/&gt;/g, '>')
                          .replace(/&amp;/g, '&')
                          .replace(/&quot;/g, '"')
                          .replace(/&apos;/g, "'")

                        paragraph.originalText += text
                        // Store direct reference to the text node
                        paragraph.textNodes.push(textEl)
                      }
                    }
                  }

                  // Handle other text-containing elements
                  if (run['w:tab']) {
                    paragraph.originalText += '\t'
                  }
                  if (run['w:br']) {
                    paragraph.originalText += '\n'
                  }
                  if (run['w:cr']) {
                    paragraph.originalText += '\r'
                  }
                }
              } else if (paraElement['w:hyperlink']) {
                // Handle hyperlinks - extract text from within hyperlink
                const hyperlinks = Array.isArray(paraElement['w:hyperlink']) ? paraElement['w:hyperlink'] : [paraElement['w:hyperlink']]
                for (const hyperlink of hyperlinks) {
                  extractTextFromParagraph(hyperlink)
                }
              } else if (paraElement['w:sdt']) {
                // Handle content controls (structured document tags)
                const sdts = Array.isArray(paraElement['w:sdt']) ? paraElement['w:sdt'] : [paraElement['w:sdt']]
                for (const sdt of sdts) {
                  if (sdt['w:sdtContent']) {
                    extractTextFromParagraph(sdt['w:sdtContent'])
                  }
                }
              } else if (paraElement['w:smartTag']) {
                // Handle smart tags
                const smartTags = Array.isArray(paraElement['w:smartTag']) ? paraElement['w:smartTag'] : [paraElement['w:smartTag']]
                for (const smartTag of smartTags) {
                  extractTextFromParagraph(smartTag)
                }
              } else {
                // Recursively search for text in other elements
                for (const key in paraElement) {
                  if (typeof paraElement[key] === 'object') {
                    extractTextFromParagraph(paraElement[key])
                  }
                }
              }
            }

            extractTextFromParagraph(obj['w:p'])

            // Only add paragraphs that have text content and should be translated
            if (paragraph.originalText.trim()) {
              // Skip auto-generated content like numbering, page numbers, etc.
              const textContent = paragraph.originalText.trim()

              // Skip if it's just numbers or bullets (auto-generated)
              const isAutoGenerated =
                /^[\d.)\-•◦▪▫]+\s*$/.test(textContent) ||
                /^[IVXLCDM]+\.\s*$/.test(textContent) || // Roman numerals
                /^[a-z]\.\s*$/.test(textContent) || // Single letters
                /^[A-Z]\.\s*$/.test(textContent) ||
                /^\d+$/.test(textContent)

              // Skip very short content that's likely auto-generated
              const isTooShort = textContent.length < 3 && !/[a-zA-Z]/.test(textContent)

              if (!isAutoGenerated && !isTooShort) {
                paragraphs.push(paragraph)
              } else {
                console.log('Skipping auto-generated content:', textContent)
              }
            }
          } else {
            // Continue traversing for other nodes
            for (const key in obj) {
              if (typeof obj[key] === 'object') {
                extractParagraphs(obj[key])
              }
            }
          }
        }

        // Extract paragraphs from ALL XML files
        for (const [filePath, jsonObj] of Object.entries(parsedXmlFiles)) {
          if (jsonObj) {
            console.log(`Extracting paragraphs from: ${filePath}`)
            extractParagraphs(jsonObj)
          }
        }

        // Prepare paragraphs for translation
        const paragraphTexts = paragraphs.map((p) => p.originalText)
        const combinedText = paragraphTexts.join('\n\n')

        // Set up translation progress tracking
        setIsChunkedTranslation(true)
        setTranslationStatus('Đang dịch tài liệu...')

        // Translate the entire document at once if possible
        if (combinedText.length <= 4000) {
          try {
            // Prepare promptValues - exclude input_language if using language detection
            const promptValues = {
              output_language: targetLanguage
            }

            // Only add input_language if not using language detection
            if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
              promptValues.input_language = sourceLanguage
            }

            const response = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                question: combinedText,
                overrideConfig: {
                  promptValues: promptValues
                }
              })
            })

            const apiResult = await response.json()
            let translatedContent = apiResult?.text

            if (translatedContent && translatedContent.trim()) {
              // Split translated content by paragraphs
              const translatedParagraphs = translatedContent.split('\n\n')

              // Apply translations to each paragraph
              paragraphs.forEach((paragraph, index) => {
                if (index < translatedParagraphs.length) {
                  const translatedText = translatedParagraphs[index].trim()

                  // Better approach: preserve formatting by distributing text across runs
                  if (paragraph.textNodes.length > 0 && translatedText) {
                    // CRITICAL: Re-encode HTML entities for XML compatibility
                    const encodedText = translatedText
                      .replace(/&/g, '&amp;')
                      .replace(/</g, '&lt;')
                      .replace(/>/g, '&gt;')
                      .replace(/"/g, '&quot;')
                      .replace(/'/g, '&apos;')

                    // If only one text node, simple replacement
                    if (paragraph.textNodes.length === 1) {
                      paragraph.textNodes[0]['#text'] = encodedText
                    } else {
                      // Multiple text nodes: put all text in first node, clear others
                      // This preserves the run structure while consolidating text
                      paragraph.textNodes[0]['#text'] = encodedText
                      for (let i = 1; i < paragraph.textNodes.length; i++) {
                        paragraph.textNodes[i]['#text'] = ''
                      }
                    }
                  }
                }
              })

              setTranslationProgress(100)
            } else {
              throw new Error('API returned empty or invalid translation for entire DOCX document')
            }
          } catch (error) {
            console.error('DOCX Translation error:', error)
            setTranslationStatus(`Lỗi khi dịch file DOCX: ${error.message}`)
            throw error
          }
        } else {
          // Process in chunks if the document is too large
          // Group paragraphs into chunks for translation
          const chunks = []
          let currentChunk = {
            paragraphs: [],
            text: ''
          }

          for (const paragraph of paragraphs) {
            // If adding this paragraph would exceed the limit, start a new chunk
            if (currentChunk.text.length + paragraph.originalText.length + 2 > 2000) {
              if (currentChunk.paragraphs.length > 0) {
                chunks.push(currentChunk)
              }
              currentChunk = {
                paragraphs: [],
                text: ''
              }
            }

            currentChunk.paragraphs.push(paragraph)
            currentChunk.text += (currentChunk.text ? '\n\n' : '') + paragraph.originalText
          }

          // Add the last chunk if it has paragraphs
          if (currentChunk.paragraphs.length > 0) {
            chunks.push(currentChunk)
          }

          // Update total chunks for progress tracking
          setTotalChunks(chunks.length)
          setProcessedChunks(0)

          // Define the chunk processing function
          const processChunk = async (chunk, chunkIndex) => {
            try {
              // Prepare promptValues - exclude input_language if using language detection
              const promptValues = {
                output_language: targetLanguage
              }

              // Only add input_language if not using language detection
              if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
                promptValues.input_language = sourceLanguage
              }

              const response = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  question: chunk.text,
                  overrideConfig: {
                    promptValues: promptValues
                  }
                })
              })

              const apiResult = await response.json()
              let translatedContent = apiResult?.text

              if (translatedContent && translatedContent.trim()) {
                // Split translated content by paragraphs - use same logic as single document
                const translatedParagraphs = translatedContent.split('\n\n')

                // Apply translations to each paragraph in this chunk
                chunk.paragraphs.forEach((paragraph, index) => {
                  if (index < translatedParagraphs.length) {
                    const translatedText = translatedParagraphs[index].trim()

                    // CRITICAL: Apply same encoding as single document translation
                    if (paragraph.textNodes.length > 0 && translatedText) {
                      // CRITICAL: Re-encode HTML entities for XML compatibility
                      const encodedText = translatedText
                        .replace(/&/g, '&amp;')
                        .replace(/</g, '&lt;')
                        .replace(/>/g, '&gt;')
                        .replace(/"/g, '&quot;')
                        .replace(/'/g, '&apos;')

                      paragraph.textNodes[0]['#text'] = encodedText

                      // Clear other text nodes in this paragraph
                      for (let i = 1; i < paragraph.textNodes.length; i++) {
                        paragraph.textNodes[i]['#text'] = ''
                      }
                    }
                  }
                })

                return {
                  chunkIndex,
                  success: true,
                  translatedParagraphs
                }
              } else {
                // If translation failed, throw error to trigger retry
                throw new Error('API returned empty or invalid translation for DOCX chunk')
              }
            } catch (error) {
              console.error(`DOCX Translation error for chunk ${chunkIndex}:`, error)

              // Retry once with simpler prompt
              try {
                // Prepare promptValues for retry - exclude input_language if using language detection
                const retryPromptValues = {
                  output_language: targetLanguage
                }

                // Only add input_language if not using language detection
                if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
                  retryPromptValues.input_language = sourceLanguage
                }

                const retryResponse = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                    question: chunk.text,
                    overrideConfig: {
                      promptValues: retryPromptValues
                    }
                  })
                })

                const retryResult = await retryResponse.json()
                let retryTranslation = retryResult?.json?.translated_text

                if (retryTranslation && retryTranslation.trim()) {
                  // Split and apply retry translation - use same logic as single document
                  const translatedParagraphs = retryTranslation.split('\n\n')

                  chunk.paragraphs.forEach((paragraph, index) => {
                    if (index < translatedParagraphs.length) {
                      const translatedText = translatedParagraphs[index].trim()

                      if (paragraph.textNodes.length > 0 && translatedText) {
                        // CRITICAL: Re-encode HTML entities for XML compatibility
                        const encodedText = translatedText
                          .replace(/&/g, '&amp;')
                          .replace(/</g, '&lt;')
                          .replace(/>/g, '&gt;')
                          .replace(/"/g, '&quot;')
                          .replace(/'/g, '&apos;')

                        paragraph.textNodes[0]['#text'] = encodedText

                        for (let i = 1; i < paragraph.textNodes.length; i++) {
                          paragraph.textNodes[i]['#text'] = ''
                        }
                      }
                    }
                  })

                  return {
                    chunkIndex,
                    success: true,
                    translatedParagraphs,
                    retried: true
                  }
                }
              } catch (retryError) {
                console.error(`DOCX Retry failed for chunk ${chunkIndex}:`, retryError)
              }

              // If both attempts failed, throw error to stop the process
              throw new Error(`Failed to translate DOCX chunk ${chunkIndex}: ${error.message}`)
            }
          }

          // Update progress function for DOCX files
          const updateProgress = (completedCount, totalChunks) => {
            setProcessedChunks(completedCount)
            setTranslationProgress((completedCount / totalChunks) * 100)

            // Add to chunk details for debugging/tracking
            setChunkDetails((prev) => [
              ...prev,
              {
                index: completedCount - 1,
                status: 'completed'
              }
            ])
          }

          // Process chunks in parallel with max 5 concurrent requests
          try {
            const results = await processChunksInParallel(chunks, 5, processChunk, updateProgress)

            // Check if all chunks were processed successfully
            const failedChunks = results.filter((r) => r.success === false || r.error)
            if (failedChunks.length > 0) {
              throw new Error(`${failedChunks.length} DOCX chunks failed to translate`)
            }

            console.log(`Successfully translated ${results.length} DOCX chunks`)
          } catch (error) {
            console.error('DOCX translation failed:', error)
            setTranslationStatus(`Lỗi khi dịch file DOCX: ${error.message}`)
            throw error
          }
        }

        // Translation complete
        setTranslationStatus('Đã hoàn thành dịch tài liệu')
        setTranslationProgress(100)

        // Convert JSON back to XML with proper DOCX formatting
        const xmlBuilder = new XMLBuilder({
          ignoreAttributes: false,
          attributeNamePrefix: '@_',
          textNodeName: '#text',
          preserveOrder: true,
          suppressEmptyNode: false,
          suppressUnpairedNode: false,
          suppressBooleanAttributes: false,
          format: false,
          indentBy: '',
          oneListGroup: false
        })
        const newDocumentXml = xmlBuilder.build(documentJsonObj)

        // Enhanced XML validation and debugging
        console.log('Generated XML structure preview:', newDocumentXml.substring(0, 500) + '...')

        // Comprehensive XML validation
        if (!newDocumentXml || typeof newDocumentXml !== 'string') {
          throw new Error('Generated XML is null or not a string')
        }

        if (!newDocumentXml.includes('<w:document')) {
          throw new Error('Generated XML is missing document root element')
        }

        if (!newDocumentXml.includes('xmlns:w=')) {
          throw new Error('Generated XML is missing required namespace declarations')
        }

        // Comprehensive XML structure validation
        const xmlValidation = {
          hasDocument: newDocumentXml.includes('<w:document'),
          hasBody: newDocumentXml.includes('<w:body'),
          hasParagraphs: newDocumentXml.includes('<w:p'),
          hasRuns: newDocumentXml.includes('<w:r'),
          hasText: newDocumentXml.includes('<w:t'),
          hasNamespaces: newDocumentXml.includes('xmlns:w='),
          openTags: (newDocumentXml.match(/</g) || []).length,
          closeTags: (newDocumentXml.match(/>/g) || []).length
        }

        console.log('XML Structure Validation:', xmlValidation)

        // Check for missing critical elements
        if (!xmlValidation.hasDocument) throw new Error('Missing w:document element')
        if (!xmlValidation.hasBody) throw new Error('Missing w:body element')
        if (!xmlValidation.hasParagraphs) console.warn('No paragraphs found in document')
        if (!xmlValidation.hasNamespaces) throw new Error('Missing XML namespaces')

        // Check tag balance (allow tolerance for self-closing tags)
        if (Math.abs(xmlValidation.openTags - xmlValidation.closeTags) > 10) {
          console.warn('XML tag mismatch detected - may cause format errors')
        }

        // Rebuild ALL modified XML files
        const rebuiltXmlFiles = {}

        // Rebuild document.xml
        rebuiltXmlFiles['word/document.xml'] = newDocumentXml

        // Rebuild other XML files if they were modified
        for (const [filePath, jsonObj] of Object.entries(parsedXmlFiles)) {
          if (filePath !== 'word/document.xml' && jsonObj) {
            try {
              const rebuiltXml = xmlBuilder.build(jsonObj)
              rebuiltXmlFiles[filePath] = rebuiltXml
              console.log(`Rebuilt: ${filePath}`)
            } catch (error) {
              console.warn(`Failed to rebuild ${filePath}:`, error)
              // If rebuild fails, keep original content to prevent corruption
              rebuiltXmlFiles[filePath] = xmlFiles[filePath]
            }
          }
        }

        // Replace ALL XML files in ZIP
        for (const [filePath, xmlContent] of Object.entries(rebuiltXmlFiles)) {
          docxZip.file(filePath, xmlContent)
        }
        const newDocxBlob = await docxZip.generateAsync({ type: 'blob' })
        const translatedFileName = originalFileName.replace(/\.[^/.]+$/, '') + '_translated.docx'

        return { blob: newDocxBlob, fileName: translatedFileName }
      } catch (error) {
        console.error('Error creating translated DOCX file:', error)
        setTranslationStatus('Lỗi khi dịch tài liệu')
        throw error
      }
    },
    [
      TRANSLATION_API_FILE_ENDPOINT,
      setIsChunkedTranslation,
      setTranslationStatus,
      setTranslationProgress,
      setTotalChunks,
      setProcessedChunks,
      setChunkDetails,
      processChunksInParallel
    ]
  )

  const readFileAsArrayBuffer = useCallback((file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.onerror = (e) => reject(e)
      reader.readAsArrayBuffer(file)
    })
  }, [])

  // Function to translate file content directly
  const translateFileContent = useCallback(
    async (content, sourceLanguage, targetLanguage) => {
      try {
        // Prepare promptValues - exclude input_language if using language detection
        const promptValues = {
          output_language: targetLanguage
        }

        // Only add input_language if not using language detection
        if (sourceLanguage !== 'Phát hiện ngôn ngữ') {
          promptValues.input_language = sourceLanguage
        }

        const response = await fetch(TRANSLATION_API_FILE_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            question: content.trim(),
            overrideConfig: {
              promptValues: promptValues
            }
          })
        })

        const result = await response.json()
        let translatedText = result?.text

        // Decode HTML entities before returning to user
        return translatedText ? decodeHtmlEntities(translatedText) : null
      } catch (error) {
        console.error('Translation error:', error)
        return null
      }
    },
    [TRANSLATION_API_FILE_ENDPOINT]
  )

  // Function to create translated TXT file
  const createTranslatedTxtFile = useCallback(async (_originalContent, translatedContent, originalFileName) => {
    const fileName = originalFileName.replace(/\.[^/.]+$/, '') + '_translated.txt'
    const blob = new Blob([translatedContent], { type: 'text/plain;charset=utf-8' })
    return { blob, fileName }
  }, [])

  const processFile = useCallback(
    async (file) => {
      if (!file) return

      const fileType = file.type
      const fileName = file.name.toLowerCase()

      if (!fileName.endsWith('.txt') && !fileName.endsWith('.docx')) {
        alert('Chỉ hỗ trợ file .txt và .docx')
        return
      }

      // Reset all progress states when starting new file processing
      setIsProcessing(true)
      setHasTranslatedFile(false)
      setTranslationStatus('Đang đọc file...')
      setTranslationProgress(0)
      setProcessedChunks(0)
      setTotalChunks(0)
      setIsChunkedTranslation(false)
      setChunkDetails([])
      setTranslatedFileBlob(null)
      setTranslatedFileName('')

      try {
        let content = ''
        let fileBuffer = null

        if (fileName.endsWith('.txt') || fileType === 'text/plain') {
          content = await readTextFile(file)
        } else if (fileName.endsWith('.docx') || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          content = await readDocxFile(file)
          fileBuffer = await readFileAsArrayBuffer(file)
        }

        if (content) {
          setOriginalFile(file)
          setOriginalFileBuffer(fileBuffer)

          let translatedFile

          if (fileName.endsWith('.docx')) {
            setTranslationStatus('Đang xử lý file DOCX...')
            translatedFile = await createTranslatedDocxFile(fileBuffer, file.name, leftLang, rightLang)
          } else {
            const needsChunking = content.length > 2000
            setIsChunkedTranslation(needsChunking)

            if (needsChunking) {
              setTranslationStatus('Đang chia nhỏ file để xử lý...')
              const chunks = createTextChunks(content, 2000)
              setTotalChunks(chunks.length)
              setChunkDetails(
                chunks.map((chunk) => ({
                  index: chunk.index,
                  size: chunk.text.length,
                  status: 'pending'
                }))
              )

              setTranslationStatus(`Đang dịch ${chunks.length} phần của file`)
              try {
                const translatedChunks = await translateChunksInParallel(chunks, leftLang, rightLang, (completed, total) => {
                  setProcessedChunks(completed)
                  setTranslationProgress((completed / total) * 100)
                  setTranslationStatus(`Đã dịch ${completed}/${total} phần...`)
                })

                if (!translatedChunks || translatedChunks.length === 0) {
                  throw new Error('Không có phần nào được dịch thành công')
                }

                setTranslationStatus('Đang ghép nối các phần đã dịch...')
                const translatedContent = combineTranslatedChunks(translatedChunks)

                if (translatedContent) {
                  setTranslationStatus('Đang tạo file đã dịch...')
                  translatedFile = await createTranslatedTxtFile(content, translatedContent, file.name)
                } else {
                  setTranslationStatus('Lỗi khi ghép nối các phần đã dịch. Vui lòng thử lại.')
                  return
                }
              } catch (chunkError) {
                console.error('Error in chunked translation:', chunkError)
                setTranslationStatus(`Lỗi khi dịch file: ${chunkError.message}`)
                return
              }
            } else {
              setTranslationStatus('Đang dịch nội dung...')
              const translatedContent = await translateFileContent(content, leftLang, rightLang)

              if (translatedContent) {
                setTranslationStatus('Đang tạo file đã dịch...')
                translatedFile = await createTranslatedTxtFile(content, translatedContent, file.name)
              } else {
                setTranslationStatus('Lỗi khi dịch nội dung. Vui lòng thử lại.')
                return
              }
            }
          }

          if (translatedFile) {
            setTranslatedFileBlob(translatedFile.blob)
            setTranslatedFileName(translatedFile.fileName)
            setHasTranslatedFile(true)
            setTranslationStatus('Hoàn thành! File đã sẵn sàng để tải về.')
            // Keep progress at 100% when completed successfully
            setTranslationProgress(100)
          }
        }
      } catch (error) {
        console.error('Error processing file:', error)
        setTranslationStatus('Lỗi khi xử lý file. Vui lòng thử lại.')
      } finally {
        setIsProcessing(false)
        // Don't reset progress here - let it stay at 100% when completed successfully
        // Only reset when starting a new translation
      }
    },
    [
      readTextFile,
      readDocxFile,
      readFileAsArrayBuffer,
      leftLang,
      rightLang,
      createTextChunks,
      translateChunksInParallel,
      combineTranslatedChunks,
      translateFileContent,
      createTranslatedTxtFile,
      createTranslatedDocxFile
    ]
  )

  // Drag and drop handlers
  const handleDragOver = useCallback(
    (e) => {
      e.preventDefault()
      e.stopPropagation()

      // Only allow drag over if not processing
      if (!isProcessing) {
        setIsDragOver(true)
      }
    },
    [isProcessing]
  )

  const handleDragLeave = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault()
      e.stopPropagation()
      setIsDragOver(false)

      // Prevent file drop if currently processing
      if (isProcessing) {
        alert('Đang dịch file hiện tại. Vui lòng đợi hoàn thành trước khi upload file mới.')
        return
      }

      const files = Array.from(e.dataTransfer.files)
      if (files.length > 0) {
        processFile(files[0])
      }
    },
    [processFile, isProcessing]
  )

  // File input handler
  const handleFileInputChange = useCallback(
    (e) => {
      // Prevent file selection if currently processing
      if (isProcessing) {
        e.target.value = ''
        return
      }

      const files = Array.from(e.target.files)
      if (files.length > 0) {
        processFile(files[0])
      }
      e.target.value = ''
    },
    [processFile, isProcessing]
  )

  const handleFileUploadClick = useCallback(() => {
    // Prevent file upload click if currently processing
    if (isProcessing) {
      return
    }
    fileInputRef.current?.click()
  }, [isProcessing])

  const handleLanguageExchange = useCallback(() => {
    // Prevent language switching while any translation is in progress
    if (isProcessing || isTranslating) {
      alert('Đang dịch. Vui lòng đợi hoàn thành trước khi đổi ngôn ngữ.')
      return
    }

    // Swap languages
    setLeftLang(rightLang)
    setRightLang(leftLang)

    // Swap text content and translated text
    const tempContent = textContent
    setTextContent(translatedText)
    setTranslatedText(tempContent)

    // Clear phonetic text when swapping languages
    setPhoneticText('')

    // Reset file translation state
    resetFileState()
    setTranslatedFileBlob(null)
    setTranslatedFileName('')
    setTranslationStatus('')
  }, [leftLang, rightLang, textContent, translatedText, resetFileState, isProcessing, isTranslating])

  // Download translated file function
  const downloadTranslatedFile = useCallback(() => {
    if (!translatedFileBlob || !translatedFileName) return
    saveAs(translatedFileBlob, translatedFileName)
  }, [translatedFileBlob, translatedFileName])

  useEffect(() => {
    if (textFieldRef.current) {
      const textArea = textFieldRef.current.querySelector('textarea')
      if (textArea) {
        textAreaRef.current = textArea
        updatePaperHeight()

        const resizeObserver = new ResizeObserver(() => {
          updatePaperHeight()
        })

        resizeObserver.observe(textArea)
        return () => resizeObserver.disconnect()
      }
    }
  }, [updatePaperHeight])

  useEffect(() => {
    if (textContent) {
      updatePaperHeight()
    }
  }, [textContent, updatePaperHeight])

  // Handle default language when switching tabs
  useEffect(() => {
    if (activeTab === 'Tài liệu' && leftLang !== 'Phát hiện ngôn ngữ') {
      setLeftLang('Phát hiện ngôn ngữ')
    } else if (activeTab === 'Văn bản' && leftLang === 'Phát hiện ngôn ngữ') {
      setLeftLang('Việt')
    }
  }, [activeTab])

  useEffect(() => {
    // Only call text translation API when on text translation tab
    if (textContent && activeTab === 'Văn bản') {
      debouncedTranslate(textContent, leftLang, rightLang)
    }
  }, [leftLang, rightLang, textContent, debouncedTranslate, activeTab])

  return (
    <>
      <Stack sx={{ width: '100%', height: 'calc(100vh - 57px)', p: 4, overflow: 'auto' }}>
        {/* Top Tabs and Layout Toggle */}
        <Stack direction='row' justifyContent='space-between' alignItems='center' mb={2}>
          <Stack direction='row' spacing={2} sx={{ overflow: 'auto' }}>
            {tabs.map((tab) => (
              <Paper
                key={tab}
                onClick={() => setActiveTab(tab)}
                sx={{
                  px: 2,
                  py: 1,
                  cursor: 'pointer',
                  backgroundColor: activeTab === tab ? '#e0f7fa' : '#fff',
                  border: activeTab === tab ? '1px solid #00acc1' : '1px solid #e0e0e0'
                }}
              >
                <Typography variant='body2'>{tab}</Typography>
              </Paper>
            ))}
          </Stack>
          <ToggleButtonGroup value={layout} exclusive onChange={handleLayoutChange} size='small'>
            <ToggleButton value='horizontal'>
              <IconLayoutDistributeHorizontal size={20} />
            </ToggleButton>
            <ToggleButton value='vertical'>
              <IconLayoutDistributeVertical size={20} />
            </ToggleButton>
          </ToggleButtonGroup>
        </Stack>

        <Box
          sx={{
            display: 'flex',
            flexDirection: layout === 'vertical' ? 'column' : 'row',
            gap: 2,
            height: 'calc(100 - 180px)'
          }}
        >
          <Paper
            elevation={0}
            sx={{
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              flex: 1,
              width: layout === 'vertical' ? '100%' : '50%',
              height: `${paperHeight}px`,
              transition: 'height 0.3s ease',
              minHeight: '200px',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ display: 'flex', p: 2, borderBottom: '1px solid #dadce0' }}>
              <Stack direction='row' spacing={2}>
                {(activeTab === 'Văn bản' ? textLanguages : fileLanguages).map((lang) => (
                  <Typography
                    key={lang}
                    onClick={() => {
                      if (isProcessing || isTranslating) {
                        alert('Đang dịch. Vui lòng đợi hoàn thành trước khi đổi ngôn ngữ.')
                        return
                      }
                      setLeftLang(lang)
                    }}
                    sx={{
                      cursor: isProcessing || isTranslating ? 'not-allowed' : 'pointer',
                      color: leftLang === lang ? '#1a73e8' : isProcessing ? '#bdc1c6' : '#5f6368',
                      fontWeight: leftLang === lang ? 500 : 400,
                      '&:hover': { color: isProcessing ? (leftLang === lang ? '#1a73e8' : '#bdc1c6') : '#1a73e8' },
                      opacity: isProcessing ? 0.6 : 1
                    }}
                  >
                    {lang}
                  </Typography>
                ))}
              </Stack>
            </Box>
            {activeTab === 'Văn bản' ? (
              <TextField
                ref={textFieldRef}
                fullWidth
                multiline
                value={textContent}
                onChange={handleTextChange}
                placeholder='Nhập văn bản cần dịch...'
                variant='standard'
                sx={{
                  p: 2,
                  '& .MuiInputBase-root': {
                    fontSize: '18px',
                    lineHeight: '32px',
                    height: 'auto'
                  },
                  '& .MuiInputBase-input': {
                    overflow: 'hidden',
                    resize: 'none'
                  },
                  '& .MuiInput-underline:before': { display: 'none' },
                  '& .MuiInput-underline:after': { display: 'none' }
                }}
              />
            ) : (
              <Box
                onDragOver={isProcessing ? undefined : handleDragOver}
                onDragLeave={isProcessing ? undefined : handleDragLeave}
                onDrop={isProcessing ? undefined : handleDrop}
                onClick={isProcessing ? undefined : handleFileUploadClick}
                sx={{
                  p: 4,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 2,
                  m: 2,
                  cursor: isProcessing ? 'not-allowed' : 'pointer',
                  backgroundColor: isDragOver && !isProcessing ? 'rgba(26, 115, 232, 0.04)' : 'transparent',
                  transition: 'all 0.2s ease',
                  opacity: isProcessing ? 0.7 : 1,
                  '&:hover': {
                    borderColor: isProcessing ? 'inherit' : '#1a73e8',
                    backgroundColor: isProcessing ? 'transparent' : 'rgba(26, 115, 232, 0.04)'
                  },
                  height: '195px',
                  justifyContent: 'center'
                }}
              >
                {isProcessing || (hasTranslatedFile && translationProgress > 0) ? (
                  <>
                    {isProcessing && <CircularProgress size={40} />}
                    <Typography color='#5f6368' textAlign='center' sx={{ mt: 2 }}>
                      {translationStatus || 'Đang xử lý file...'}
                    </Typography>
                    {isChunkedTranslation && totalChunks > 0 && (
                      <Box sx={{ width: '100%', mt: 2 }}>
                        <LinearProgress
                          variant='determinate'
                          value={translationProgress}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: '#e0e0e0',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#1a73e8'
                            }
                          }}
                        />
                        <Typography variant='caption' color='#5f6368' textAlign='center' sx={{ mt: 1, display: 'block' }}>
                          {processedChunks}/{totalChunks} phần đã hoàn thành ({Math.round(translationProgress)}%)
                        </Typography>
                      </Box>
                    )}
                    {!isProcessing && hasTranslatedFile && (
                      <Button variant='outlined' size='small' onClick={resetFileState} sx={{ mt: 2 }}>
                        Dịch file mới
                      </Button>
                    )}
                  </>
                ) : (
                  <>
                    <IconUpload size={48} color={isProcessing ? '#ccc' : '#5f6368'} />
                    <Typography color={isProcessing ? '#ccc' : '#5f6368'} textAlign='center'>
                      {isProcessing ? 'Đang dịch file, vui lòng đợi...' : 'Kéo và thả tài liệu của bạn vào đây'}
                    </Typography>
                    <Typography variant='caption' color={isProcessing ? '#ccc' : '#9aa0a6'} textAlign='center'>
                      {isProcessing
                        ? 'Upload file mới sẽ khả dụng sau khi hoàn thành'
                        : 'Hỗ trợ file .txt và .docx - File sẽ được dịch và tự động tải về'}
                    </Typography>
                    <Button
                      variant='outlined'
                      size='small'
                      disabled={isProcessing}
                      sx={{
                        mt: 1,
                        opacity: isProcessing ? 0.5 : 1
                      }}
                    >
                      {isProcessing ? 'Đang dịch...' : 'Chọn file từ thiết bị'}
                    </Button>
                  </>
                )}
                <input ref={fileInputRef} type='file' accept='.txt,.docx' onChange={handleFileInputChange} style={{ display: 'none' }} />
              </Box>
            )}
          </Paper>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 1,
              alignSelf: layout === 'vertical' ? 'center' : 'center'
            }}
          >
            <IconButton
              disabled={isProcessing || isTranslating}
              sx={{
                backgroundColor: isProcessing ? '#f5f5f5' : '#fff',
                boxShadow: '0 1px 3px rgba(0,0,0,0.12)',
                '&:hover': { backgroundColor: isProcessing ? '#f5f5f5' : '#f8f9fa' },
                transform: layout === 'vertical' ? 'rotate(90deg)' : 'none',
                cursor: isProcessing || isTranslating ? 'not-allowed' : 'pointer'
              }}
              onClick={handleLanguageExchange}
            >
              <IconArrowsExchange size={24} color={isProcessing ? '#bdc1c6' : '#5f6368'} />
            </IconButton>
          </Box>

          <Paper
            elevation={0}
            sx={{
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              flex: 1,
              width: layout === 'vertical' ? '100%' : '50%',
              height: `${paperHeight}px`,
              transition: 'height 0.3s ease',
              minHeight: '200px',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ display: 'flex', p: 2, borderBottom: '1px solid #dadce0' }}>
              <Stack direction='row' spacing={2}>
                {(activeTab === 'Văn bản' ? textLanguages : textLanguages).map((lang) => (
                  <Typography
                    key={lang}
                    onClick={() => {
                      if (isProcessing || isTranslating) {
                        alert('Đang dịch. Vui lòng đợi hoàn thành trước khi đổi ngôn ngữ.')
                        return
                      }
                      setRightLang(lang)
                    }}
                    sx={{
                      cursor: isProcessing || isTranslating ? 'not-allowed' : 'pointer',
                      color: rightLang === lang ? '#1a73e8' : isProcessing ? '#bdc1c6' : '#5f6368',
                      fontWeight: rightLang === lang ? 500 : 400,
                      '&:hover': { color: isProcessing ? (rightLang === lang ? '#1a73e8' : '#bdc1c6') : '#1a73e8' },
                      opacity: isProcessing ? 0.6 : 1
                    }}
                  >
                    {lang}
                  </Typography>
                ))}
              </Stack>
            </Box>
            <Box sx={{ p: 2, height: 'calc(100% - 56px)', overflow: 'auto', position: 'relative' }}>
              {isTranslating ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  {activeTab === 'Văn bản' ? (
                    <PhoneticDisplay
                      originalText={translatedText}
                      phoneticText={phoneticText}
                      language={rightLang}
                      showPhonetic={showPhonetic}
                      onTogglePhonetic={setShowPhonetic}
                    />
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                        gap: 2
                      }}
                    >
                      {hasTranslatedFile && originalFile ? (
                        <>
                          <Alert severity='success' sx={{ width: '100%', mb: 2 }}>
                            <Typography variant='body2'>File {originalFile.name} đã được dịch thành công!</Typography>
                            <Typography variant='caption' sx={{ display: 'block', mt: 1 }}>
                              {translationStatus}
                            </Typography>
                          </Alert>
                          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                            <Button
                              variant='contained'
                              size='large'
                              startIcon={<IconDownload size={20} />}
                              onClick={downloadTranslatedFile}
                              sx={{
                                backgroundColor: '#1a73e8',
                                '&:hover': { backgroundColor: '#1557b0' },
                                px: 4,
                                py: 1.5
                              }}
                            >
                              Tải lại file đã dịch
                            </Button>
                          </Box>
                        </>
                      ) : (
                        <Typography variant='body1' sx={{ fontSize: '18px', color: '#5f6368', textAlign: 'center' }}>
                          Kéo thả file vào bên trái để bắt đầu dịch
                        </Typography>
                      )}
                    </Box>
                  )}

                  {/* Show download buttons for text translation mode */}
                  {activeTab === 'Văn bản' && hasTranslatedFile && translatedText && originalFile && (
                    <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
                      <Typography variant='caption' sx={{ color: '#5f6368', mr: 1 }}>
                        File gốc: {originalFile.name}
                      </Typography>
                      <Button
                        variant='contained'
                        size='small'
                        startIcon={<IconDownload size={16} />}
                        onClick={downloadTranslatedFile}
                        sx={{
                          backgroundColor: '#1a73e8',
                          '&:hover': { backgroundColor: '#1557b0' }
                        }}
                      >
                        Tải file đã dịch ({originalFile.name.toLowerCase().endsWith('.txt') ? 'TXT' : 'DOCX'})
                      </Button>
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Paper>
        </Box>
      </Stack>
      <ModalPreviewImage image={previewImage.image} open={previewImage.open} onClose={() => setPreviewImage({ open: false, image: '' })} />
    </>
  )
}

export default memo(ChatContent)
