import pinyinLib from 'pinyin'
import * as wanakana from 'wanakana'

// Use the correct pinyin function
const pinyin = pinyinLib.default || pinyinLib

/**
 * Convert Chinese text to Pinyin with tone marks
 * @param {string} text - Chinese text to convert
 * @returns {string} - Pinyin with tone marks
 */
export const chineseToPinyin = (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    // Remove non-Chinese characters for processing but keep them in result
    const chineseRegex = /[\u4e00-\u9fff]/g
    const matches = text.match(chineseRegex)

    if (!matches || matches.length === 0) {
      return ''
    }

    // Convert Chinese characters to Pinyin with tone marks
    const pinyinResult = pinyin(text, {
      style: pinyin.STYLE_TONE, // Use tone marks (ā, á, ǎ, à)
      heteronym: false, // Use most common pronunciation
      segment: true // Enable word segmentation
    })

    // Flatten and join the pinyin result
    return pinyinResult.map((item) => (Array.isArray(item) ? item[0] : item)).join(' ')
  } catch (error) {
    console.error('Error converting Chinese to Pinyin:', error)
    return ''
  }
}

/**
 * Convert Japanese text to Romaji
 * @param {string} text - Japanese text to convert
 * @returns {Promise<string>} - Romaji transcription
 */
export const japaneseToRomaji = async (text) => {
  try {
    if (!text || typeof text !== 'string') {
      return ''
    }

    // Check if text contains Japanese characters
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]/g
    if (!japaneseRegex.test(text)) {
      return ''
    }

    // Use wanakana to convert Japanese to Romaji
    // This handles Hiragana and Katakana well, but not Kanji
    // For a more complete solution, we'd need a more sophisticated library
    const romajiResult = wanakana.toRomaji(text, {
      customKanaMapping: {},
      IMEMode: false
    })

    return romajiResult
  } catch (error) {
    console.error('Error converting Japanese to Romaji:', error)
    return ''
  }
}

/**
 * Get phonetic transcription based on language
 * @param {string} text - Text to convert
 * @param {string} language - Target language ('Trung' for Chinese, 'Nhật' for Japanese)
 * @returns {Promise<string>} - Phonetic transcription
 */
export const getPhoneticTranscription = async (text, language) => {
  try {
    if (!text || !language) {
      return ''
    }

    switch (language) {
      case 'Trung':
        return chineseToPinyin(text)
      case 'Nhật':
        return await japaneseToRomaji(text)
      default:
        return ''
    }
  } catch (error) {
    console.error('Error getting phonetic transcription:', error)
    return ''
  }
}

/**
 * Check if a language supports phonetic transcription
 * @param {string} language - Language to check
 * @returns {boolean} - Whether the language supports phonetic transcription
 */
export const supportsPhoneticTranscription = (language) => {
  return language === 'Trung' || language === 'Nhật'
}

/**
 * Format text with phonetic transcription for display
 * @param {string} originalText - Original translated text
 * @param {string} phoneticText - Phonetic transcription
 * @param {string} language - Target language
 * @returns {object} - Formatted display object
 */
export const formatPhoneticDisplay = (originalText, phoneticText, language) => {
  return {
    original: originalText,
    phonetic: phoneticText,
    language: language,
    hasPhonetic: !!phoneticText && phoneticText.trim().length > 0
  }
}
